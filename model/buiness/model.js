module.exports = {
  model: 'dashboard',
  name: '电商系统',
  menu: [
    {
      key: 'product',
      name: '商品管理',
      menuType: 'module',
      moduleType: 'schema',
      schemaConfig: {
        api: '/api/proj/product',
        schema: {
          type: 'object',
          properties: {
            product_id: {
              type: 'string',
              label: '商品ID',
              tableOption: {
                width: 200,
                'show-overflow-tooltip': true,
              },
              editFormOption: {
                comType: 'input',
                disabled: true
              },
              detailPanelOption: {}
            },
            product_name: {
              type: 'string',
              label: '商品名称',
              maxLength: 10,
              minLength: 2,
              tableOption: { width: 200 },
              searchOption: {
                comType: "dynamicSelect",
                api: "/api/proj/product_enum/list"
              },
              createFormOption: {
                comType: 'input',
                default: 'testxxxx',

              },
              editFormOption: {
                comType: 'input',
                visible: false
              },
              detailPanelOption: {}
            },
            price: {
              type: 'number',
              label: '价格',
              maximum: 1000,
              minimum: 30,
              minLength: 2,
              tableOption: { width: 200 },
              searchOption: {
                comType: "select",
                enumList: [
                  {
                    label: "全部",
                    value: -1
                  },
                  {
                    label: "¥39.9",
                    value: 39.9
                  },
                  {
                    label: "¥199",
                    value: 199
                  },
                  {
                    label: "¥699",
                    value: 699
                  }]
              },
              createFormOption: {
                comType: 'inputNumber'
              },
              editFormOption: {
                comType: 'inputNumber'
              },
              detailPanelOption: {}

            },
            inventory: {
              type: 'number',
              label: '库存',
              tableOption: { width: 200 },
              searchOption: {
                comType: 'input',
              },
              createFormOption: {
                comType: 'select',
                enumList: [{
                  label: "全部",
                  value: -1
                },
                {
                  label: "9999",
                  value: 9999
                },
                {
                  label: "777",
                  value: 777
                }]
              },
              editFormOption: {
                comType: 'select',
                enumList: [{
                  label: "全部",
                  value: -1
                },
                {
                  label: "9999",
                  value: 9999
                },
                {
                  label: "777",
                  value: 777
                }]
              },
              detailPanelOption: {}
            },
            create_time: {
              type: 'string',
              label: '创建时间',
              tableOption: {},
              searchOption: {
                comType: "dateRange"
              },
              detailPanelOption: {}
            }
          },
          required: ['product_name']
        },
        tableConfig: {
          headerButtons: [{
            label: '新增商品',
            eventKey: 'showComponent',
            eventOption: {
              comName: 'createForm'
            },
            type: 'primary',
            plain: true
          }],
          rowButtons: [{
            label: "修改",
            eventKey: 'showComponent',
            eventOption: {
              comName: 'editForm'
            },
            type: "warning"
          }, {
            label: "删除",
            eventKey: "remove",
            eventOption: {
              params: {
                product_id: 'schema::product_id'
              },
            },
            type: "danger",
          }, {
            label: "查看",
            eventKey: "showComponent",
            eventOption: {
              comName: 'detailPanel'
            },
            type: "primary",
          }]
        },
        componentConfig: {
          createForm: {
            title: "新增商品",
            saveBtnText: '新增商品'
          },
          editForm: {
            mainKey: 'product_id',
            title: "修改商品",
            saveBtnText: '修改商品'
          },
          detailPanel: {
            mainKey: 'product_id',
            title: "商品详情"
          }
        }
      }
    },
    {
      key: 'order',
      name: '订单管理',
      menuType: 'module',
      moduleType: 'custom',
      customConfig: {
        path: '/todo'
      }
    },
    {
      key: 'client',
      name: '商品管理',
      menuType: 'module',
      moduleType: 'custom',
      customConfig: {
        path: '/todo'
      }
    }
  ]
} 