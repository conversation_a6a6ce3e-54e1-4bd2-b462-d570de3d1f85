const dashboard = {
  mode: 'dashboard', // 模版类型 不同模版类型对应不一样的模版数据结构
  name: '', //名称
  desc: '', //描述
  icon: '', //图标
  homePage: '', //首页路径
  model: [
    {
      key: '', // 菜单唯一描述
      name: '', // 菜单名称
      menuType: '', // 枚举值, group / module

      // 当 menuType == group 时, 可填
      subMenu: [{
        // 可递归 menuItem
      }, ...],

      moduleType: '',  // 枚举值: sider/iframe/custom/schema

      // 当moduleType == sider 时
      siderConfig: {
        menu: [{
          // 可递归 menuItem (除 moduleType == sider)
        }, ...]
      },

      // 当moduleType == iframe 时
      iframeConfig: {
        path: '', // iframe 路径
      },

      // 当moduleType == custom 时
      customConfig: {
        path: '', // 自定义组件路径
      },

      // 当moduleType == schema 时
      schemaConfig: {
        api: '', // 数据源api (遵循 RESTFUL 规范)
        schema: { //板块数据结构
          type: 'object',
          properties: {
            key: {
              ...schema, // 标准的 schema 配置
              type: '', // 字段类型
              label: '', // 字段的中文名
              // 字段在 table 中的相关配置
              tableOption: {
                ...elTableColumnCibfig, //  标准 el-table-column 配置
                toFixed: 0, // 保留小数点位数
                visible: true, // 默认为 true (false 或 不配置时, 表示不在表单中显示)
              },
              // 字段在 search-bar 中的相关配置
              searchOption: {
                ...eleComponentConfig, // 标准 el-component-column 配置
                comType: '', // 配置组件类型 input/select/....
                default: '', // 默认值
                enumList: [], // 下拉框可选项
                api: ""
              },
              // 字段在不同动态 component 中的相关配置,前缀对应 componentComfig 中的键值
              // 如:componentConfig.createForm,这里对应 createFormOption
              // 字段在 createForm 中的配置
              createFormOption: {
                ...eleComponentConfig, // 标准 el-component-column 配置
                comType: '', // 控件类型 input/select/....
                visible: true, // 默认为 true (false 或 不配置时, 表示不在表单中显示)
                disabled: false, // 默认为 false (true 或 不配置时, 表示控件禁用)
                default: '',// 默认值                
                // comType === 'select' 时生效
                enumList: [], // 枚举列表
              },
              // 字段在 editForm 中的配置
              editFromOption: {
                ...eleComponentConfig, // 标准 el-component-column 配置
                comType: '', // 控件类型 input/select/....
                visible: true, // 默认为 true (false 或 不配置时, 表示不在表单中显示)
                disabled: false, // 默认为 false (true 或 不配置时, 表示控件禁用)
                default: '',// 默认值    
                // comType === 'select' 时生效
                enumList: [], // 枚举列表
              },
              detailPanelOption: {
                ...eleComponentConfig, // 标准 el-component-column 配置
              }
            },
            ...
          },
          required: []
        },
        tableCofig: {
          headerButtons: [{
            label: '',
            eventKey: '', // 按钮事件名
            eventOption: {
              // 当 eventKey == 'showComponent' 时生效
              comName: '', // 组件名称
            }, // 按钮具体配置
            ...elButtonsConfig // 标准 el-button 配置
          }, ...],
          rowButton: [{
            label: '',
            eventKey: '', // 按钮事件名
            eventOption: {
              // 当 eventKey == 'showComponent' 时生效
              comName: '', // 组件名称

              // 当 eventKey == 'remove
              params: {
                // params = 参数的键值 
                // rowValueKey = 参数值 (当格式为 schema::tableKey 的时候,到 table 中找响应的字段) 
                paramsKey: rowValueKey
              }
            }, // 按钮具体配置
            ...elButtonsConfig // 标准 el-button 配置
          }, ...]
        }, // table 相关配置
        searchConfig: {}, // search-bar 相关配置
        // 动态组件 相关配置
        compontentConfig: {
          // create-form 表单相关配置
          careateForm: {
            title: '', // 表单标题
            saveBtnText: '' // 保存按钮文案
          },
          // 表单相关配置
          editFrom: {
            mainKey: "", // 主键
            title: "", // 表单标题
            saveBtnText: "", // 保存按钮文案
          },
          // detail-panel
          detailPanel: {
            mainKey: "", // 主键
            title: "", // 表单标题
          }
          // ...扩展

        },
      }

    }, ...]
}