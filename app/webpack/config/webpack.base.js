const glob = require('glob')
const path = require('path')
const webpack = require('webpack')
const { VueLoaderPlugin } = require('vue-loader')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const CleanWebpackPlugin = require('clean-webpack-plugin')

const pageEntries = {};
const htmlWebpackPluginList = [];

// 获取 app/pages 目录下的所有页面入口文件(entry.xx.js)
const entryList = path.resolve(process.cwd(), './app/pages/**/entry.*.js');
glob.sync(entryList).forEach(file => {
  const entryName = path.basename(file, '.js');
  // 构造 entry
  pageEntries[entryName] = file;
  // 构造最终渲染的页面文件
  htmlWebpackPluginList.push(new HtmlWebpackPlugin({
    // 产物最终模版
    filename: path.resolve(process.cwd(), './app/public/dist/', `${entryName}.tpl`),
    // 指定模版文件
    template: path.resolve(process.cwd(), './app/view/entry.tpl'),
    // 要注入的代码块
    chunks: [entryName]
  }))
})

/**
 * webpack 基础配置
 */
module.exports = {
  // 入口配置
  entry: pageEntries,
  // 模块解析配置
  module: {
    rules: [{
      test: /\.vue$/,
      use: {
        loader: 'vue-loader'
      }
    },
    {
      test: /\.js$/,
      include: [
        path.resolve(process.cwd(), '/app/pages') // 只对业务代码进行babel,加快打包速度
      ],
      use: {
        loader: 'babel-loader'
      }
    },
    {
      test: /\.(png|jpe?g|gif)(\?.+)?$/,
      use: {
        loader: 'url-loader',
        options: {
          limit: 300, // 小于300kb的图片会被转成base64编码
          esMoule: false // 禁用esModule
        }
      }
    }, {
      test: /\.css$/,
      use: ['style-loader', 'css-loader']
    }, {
      test: /\.less$/,
      use: ['style-loader', 'css-loader', 'less-loader']
    },
    {
      test: /\.(eot|svg|ttf|woff|woff2)(\?\S*)?$/, // 例如:file.woff2?v=abc&opt=1
      use: 'file-loader'
    }
    ]
  },
  // 产物输出路径, 因为开发和生产环境输出不一致,所以在各自环境中进行配置
  output: {},
  // 配置模块解析的具体行为(定义 webpack 在打包时,如何找到并解析具体模块的路径)
  resolve: {
    // 尝试按顺序解析这些后缀名。如果有多个文件有相同的名字，但后缀名不同，webpack 会解析列在数组首位的后缀的文件 并跳过其余的后缀
    // 能够使用户在引入模块时不带扩展：import File from '../path/to/file';
    extensions: ['.js', '.vue', '.less', '.css'],
    // 配置别名: import { xxx } from '$common/xxx';
    alias: {
      $pages: path.resolve(process.cwd(), './app/pages'), // 定义别名,方便引入业务代码
      $common: path.resolve(process.cwd(), './app/pages/common'),
      $widgets: path.resolve(process.cwd(), './app/pages/widgets'),
      $store: path.resolve(process.cwd(), './app/pages/store'),
    }
  },
  // 配置 webpack 插件
  plugins: [
    // 处理 .vue 文件,这个插件是必须de
    // 它的职能是将定义过的其他规则复制并应用到 .vue 文件中
    // 例如,如果有一条匹配规则 /\.js$/ 的规则, 那么他会应用到 .vue 文件中的 script 板块中
    new VueLoaderPlugin(),
    // 把第三方库暴露到 window context 下 
    // 任何文件都可以直接使用 Vue，Webpack 会自动将其映射为 require('vue')。
    // 例如 new Vue( { el: '#app', render: h => h(App) } );
    new webpack.ProvidePlugin({
      Vue: 'vue',
      axios: 'axios',
      _: 'lodash'
    }),
    // 定义全局常量
    new webpack.DefinePlugin({
      __VUE_OPRIONS_API__: 'true', // 禁用选项式 API 支持
      __VUE_PRO_DEVTOOLS: 'false', // 禁用 vue 调试工具
      __VUE_PRO_HYDRATION_MISMATCH_DETAILS__: 'false' // 禁用生产环境构建下激活 (hydration) 不匹配的详细警告
    }),
    // 显示打包进度
    new webpack.ProgressPlugin(),
    // 每次 build 前清空 public/dist 目录
    new CleanWebpackPlugin(['public/dist'], {
      root: path.resolve(process.cwd(), './app/'),
      exclude: [],
      verbose: true,
      dry: false
    }),
    // 构造最终渲染的页面模版
    ...htmlWebpackPluginList,
  ],
  // 配置打包输出优化(代码分割,模块分割,缓存,treeShaing,压缩等优化策略)
  optimization: {
    /**
     * 把 js 文件打包成3种类型
     * 1. verdor: 第三方 lib 库, 基本不会改动, 除非依赖版本升级
     * 2. common: 业务组件代码的公共部分抽取出来, 改动较少
     * 3. ebnty.{page}:  不同页面 entry 里的业务组件代码的差异部分,会经常改动
     * 目的: 把改动和引用频率不一样的 js 区分出来,已达到更好利用浏览器缓存的效果
     */
    splitChunks: {
      chunks: 'all', // 对同步和异步模块都进行分割
      maxAsyncRequests: 10, // 每次异步加载的最大并行请求数
      maxInitialRequests: 10, // 入口点的最大并行请求数
      cacheGroups: {
        vendor: { // 第三方库
          test: /[\\/]node_modules[\\/]/, // 打包node_modules 目录下的模块
          name: 'vendor', //模块名称
          priority: 20, // 优先级,数字越大越优先
          enforce: true, // 强制执行
          reuseExistingChunk: true, // 复用已有的公共 chunk
        },
        common: { // 业务组件公共代码
          test: /[\\/]common|widgets[\\/]/,
          name: 'common',
          minChunks: 2, // 被两处引用即被归为公共模块
          minSize: 1, // 最小分割文件大小 
          priority: 10, // 优先级
          reuseExistingChunk: true, // 复用已有的公共 chunk
        }
      },
    },
    // 将 webpack 运行时生成的代码打包到 runtime.js
    runtimeChunk: true
  },
}