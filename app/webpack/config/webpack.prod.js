const path = require('path');
const merge = require('webpack-merge');
const os = require('os');
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const CSSMinimizerPlugin = require('css-minimizer-webpack-plugin')
const HtmlWebpackInjectAttributesPlugin = require('html-webpack-inject-attributes-plugin')
const TerserWebpackPlugin = require('terser-webpack-plugin')

// 基类配置
const baseConfig = require('./webpack.base.js');

const webpackConfig = merge.smart(baseConfig, {
  // 指定生产环境
  mode: 'production',
  // 生产环境的 out put 配置
  output: {
    filename: 'js/[name]_[chunkhash:8].bundle.js',
    path: path.join(process.cwd(), './app/public/dist/prod/'),
    publicPath: '/dist/prod/', // 输出目录的公共 URL
    crossOriginLoading: 'anonymous' // 允许跨域加载
  },
  module: {
    rules: [{
      test: /\.css$/,
      use: [
        MiniCssExtractPlugin.loader,
        'css-loader',
        'thread-loader'
      ]
    }, {
      test: /\.js$/,
      include: [path.resolve(process.cwd(), './app/pages')],
      use: [{
        loader: 'thread-loader', // 多线程编译loader
        options: {
          workers: os.cpus().length, // 使用 CPU 核心数
        }
      },
      {
        loader: 'babel-loader',
        options: {
          presets: ['@babel/preset-env'], // 用于语法转换和按需引入 polyfill (处理旧版本浏览器兼容,填补 API/新特性 缺失)
          plugins: ['@babel/plugin-transform-runtime'] // 用于复用辅助代码和模块化 polyfill。
        }
      }
      ]
    }]
  },
  // performance 用于控制性能提示信息, 默认为 warning; 文件体积过大, 入口过多, 资源加载方式等情况下会提示警告
  performance: {
    hints: false
  },
  plugins: [
    // 提取 css 的公共部分, 有效利用缓存
    new MiniCssExtractPlugin({
      chunkFilename: 'css/[name]_[contenthash:8].bundle.css'
    }),
    // 优化并压缩 css
    new CSSMinimizerPlugin(),
    // 浏览器在请求资源时不发送用户的身份凭证
    new HtmlWebpackInjectAttributesPlugin({
      crossorigin: 'anonymous'
    })
  ],
  optimization: { // 优化配置
    // 使用 TerserWebpackPlugin 的并发和缓存,提升压缩阶段的性能
    minimize: true,
    minimizer: [new TerserWebpackPlugin({
      cache: true, // 启用缓存来加速构建过程
      parallel: true, // 利用多核 CPU 并行压缩
      terserOptions: {
        compress: {
          drop_console: true // 移除 console.log
        }
      }
    })]
  }
});

module.exports = webpackConfig;