module.exports = (app) => {
  const baseController = require('./base')(app);
  return class BusinessController extends baseController {
    create(ctx) {
      const { product_name: productName,
        price,
        inventory
      } = ctx.request.body

      this.success(ctx, {
        product_id: Date.now(),
        product_name: productName,
        price,
        inventory
      })
    }


    remove(ctx) {
      const { product_id: productId } = ctx.request.body;
      this.success(ctx, {
        projKey: ctx.projKey,
        product_id: productId
      })
    }
    update(ctx) {
      const {
        product_id: productId,
        product_name: productName,
        price,
        inventory
      } = ctx.request.body

      this.success(ctx, {
        product_id: productId,
        product_name: productName,
        price,
        inventory
      })

    }

    get(ctx) {
      const { product_id: productId } = ctx.request.query;
      const productList = this.getProductList(ctx);
      const productItem = productList.find(item => item.product_id === productId);
      this.success(ctx, productItem);
    }

    getList(ctx) {
      const { product_name: productName, page, size } = ctx.request.query;

      /**
       * projKey 的全局访问:
       * 1. 先通过 controller/view.js 从路由获取值, 然后解析到 entry.tpl 模版中
       * 2. 再通过 entry.tpl 注册到 windows上
       * 3. 再通过 curl 访问 window.projKey, 并统一设置到请求头 headers 上
       * 4. 然后在 project-handler.js 中间件中读取 headers 值, 统一挂载到 ctx 上
       * 5. 最后就可以通过 ctx.projKey 的方式全局访问 projKey 了
       */
      let productList = this.getProductList(ctx)

      if (productName && productName !== 'all') {
        productList = productList.filter(item => item.product_name.indexOf(productName) > -1)
      }

      this.success(ctx, productList,
        {
          total: 0,
          page,
          size
        }
      )
    }

    getProductEnumList(ctx) {
      const productEnumList = [
        { value: `all`, label: '全部' },
        { value: `大前端面试宝典`, label: `大前端面试宝典` },
        { value: `前端求职之道`, label: `前端求职之道` },
        { value: `前端全栈实践`, label: `前端全栈实践` }
      ]

      this.success(ctx, productEnumList)
    }

    getProductList(ctx) {
      return [{
        product_id: '1',
        product_name: `${ctx.projKey}-<大前端面试宝典>`,
        price: 39.9,
        inventory: 9999,
        create_time: '2021-01-01 00:00:00'
      }, {
        product_id: '2',
        product_name: '<前端求职之道>',
        price: 199,
        inventory: 9999,
        create_time: '2024-01-01 12:45:22'
      },
      {
        product_id: '3',
        product_name: '<前端全栈实践>',
        price: 699,
        inventory: 777,
        create_time: '2025-02-09 18:45:22'
      }]
    }
  }
}