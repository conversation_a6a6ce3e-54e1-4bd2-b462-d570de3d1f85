import boot from '$pages/boot.js'
import projectList from './project-list.vue';

/**
 * 项目列表页面入口文件
 * 
 * 加载流程: 
 *  1. 配置 webpack 遍历 pages 文件夹. 查找 entry.xxx.js 格式的入口文件;
 *  2. 而入口文件会调用 boot 方法, boot 文件会 createVue(page) 并且 mount('#root')、挂载路由等操作; 所以一个调用了boot()方法的入口文件,就代表了一个 SPA 页面;
 *  3. HtmlWebpackPlugin 插件会根据这些入口文件, 在 dist 目录下生成对应的 html 文件, 并引入 js,css 文件;
 *  4. 将 dist 放置在服务器下, 访问页面即可, 就像加载一个原生html一样;
 *  5. 又由于在 HtmlWebpackPlugin 打包时, 我们配置了 view/entry.tpl 为模板文件, 模版文件中声明了一个 <div id="root"></div> 节点
 *  6. 所以每个tpl产物文件,都会包含一个 id 为 root 的 div 节点, 作为页面的根节点; 
 *  7. 并且tpl产物文件在 script 中还引入 VueJs, 使得我们的业务组件和vue页面都可以渲染到root节点上并运行;
 * 
 */

boot(projectList)