<template>
  <div class="schema-table">
    <el-table
      v-if="schema && schema?.properties"
      v-loading="loading"
      :data="tableData"
      class="table"
    >
      <template v-for="(schemaItem, key) in schema.properties">
        <el-table-column
          v-if="schemaItem.option.visible !== false"
          :key="key"
          :prop="key"
          :label="schemaItem.label"
          v-bind="schemaItem.option"
        />
      </template>
      <el-table-column
        v-if="buttons?.length > 0"
        label="操作"
        fixed="right"
        :width="operationWidth"
      >
        <template #default="scope">
          <el-button
            v-for="(item, index) in buttons"
            :key="index"
            v-bind="item"
            link
            @click="operationHandler({ btnConfig: item, rowData: scope.row })"
          >
            {{ item.label }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-row justify="end" class="pagination">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="total,sizes,prev,next,jumper"
        @size-change="onPageSizeChange"
        @current-change="onCurrentPageChange"
      />
    </el-row>
  </div>
</template>

<script setup>
import { ref, toRefs, computed, watch, nextTick, onMounted } from "vue";
import $curl from "$common/curl.js";
const emit = defineEmits(["operate"]);

const props = defineProps({
  schema: Object,
  /**
   * 表格数据源 api
   * {
        type: 'object',
        properties: {
          key: {
              ...schema, // 标准的 schema 配置
              type: '', // 字段类型
              label: '', // 字段的中文名
              tableOption: {
                ...elTableColumnCibfig, //  标准 el-table-column 配置
                visible: true, // 默认为 true (false 或 不配置时, 表示不在表单中显示)
              },
          }, ...
        }
      }
   */
  api: String,
  /**
   * api 请求参数, 请求 API 时携带
   */
  apiParams: Object,

  /**
   * buttons 操作按钮相关配置,结构如下
   * [{
        label: '',
        eventKey: '', // 按钮事件名
        eventOption: {}, // 按钮具体配置
       ...elButtonsConfig // 标准 el-button 配置
    }]
   */
  buttons: Array,
});

const { schema, api, apiParams, buttons } = toRefs(props);

const operationWidth = computed(() => {
  return buttons.value?.length > 0
    ? buttons.value.reduce((pre, cur) => {
        return pre + cur.label.length * 18;
      }, 50)
    : 50;
});

const loading = ref(false);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

onMounted(() => {
  initData();
});

watch(
  [schema, api, apiParams],
  () => {
    initData();
  },
  { deep: true }
);

const initData = () => {
  currentPage.value = 1;
  pageSize.value = 50;
  nextTick(async () => {
    await loadTableData();
  });
};

const loadTableData = async () => {
  if (!api.value) {
    return;
  }

  showLoading();
  //请求 table 数据
  const res = await $curl({
    method: "get",
    url: `${api.value}/list`,
    query: {
      ...apiParams.value,
      page: currentPage.value,
      size: pageSize.value,
    },
  });
  hideLoading();

  if (!res || !res.success || !Array.isArray(res.data)) {
    tableData.value = [];
    total.value = 0;
    return;
  }

  tableData.value = buildTableData(res.data);
  total.value = res.metadata.total;
};

/**
 * 对后端返回的数据进行熏染前的处理
 * @params {object} data 列表数据
 */
const buildTableData = (buildTableData) => {
  if (!schema.value?.properties) {
    return buildTableData;
  }

  return buildTableData.map((rowData) => {
    for (const dKey in rowData) {
      const schemaItem = schema.value.properties[dKey];
      // 处理 toFixed
      if (schemaItem?.option?.toFixed) {
        rowData[dKey] = rowData[dKey].toFixed && rowData[dKey].toFixed(schemaItem.option.toFixed);
      }
    }
    return rowData;
  });
};

const showLoading = () => {
  loading.value = true;
};

const hideLoading = () => {
  loading.value = false;
};

const operationHandler = ({ btnConfig, rowData }) => {
  console.log("🚀 ~ operationHandler ~ btnConfig:", btnConfig);
  emit("operate", { btnConfig, rowData });
};

const onPageSizeChange = async (value) => {
  pageSize.value = value;
  await loadTableData();
};

const onCurrentPageChange = async (value) => {
  currentPage.value = value;
  await loadTableData();
};

defineExpose({
  initData,
  loadTableData,
  showLoading,
  hideLoading,
});
</script>

<style lang="less" scoped>
.schema-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .table {
    flex: 1;
  }

  .pagination {
    margin: 10px 0;
    text-align: right;
  }
}
</style>
