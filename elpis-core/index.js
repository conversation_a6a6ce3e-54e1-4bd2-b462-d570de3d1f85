const Koa = require('koa');
const path = require('path');
const { sep } = path; //兼容不同操作系统上的斜杠

const env = require('./env') // 业务层-环境分发

const middlewareLoader = require('./loader/middleware'); // 接入层-路由中间件
const routerSchemaLoader = require('./loader/router-schema'); // 接入层-路由规则
const routerLoader = require('./loader/router'); // 接入层-接口路由分发
const controllerLoader = require('./loader/controller'); // 业务层-处理器
const configLoader = require('./loader/config'); // 业务层-配置提取
const extendLoader = require('./loader/extend'); // 业务层-服务拓展
const serviceLoader = require('./loader/service'); // 服务层-service处理器


module.exports = {
  /**
   * 启动项目
   * @params options 启动参数
   * options = {
   *  name: '', // 项目名称
   *  homePage:'' // 项目主页
   * }
   */
  start(options = {}) {
    // koa 实例
    const app = new Koa();

    // 应用配置
    app.options = options;

    // 基本路径
    app.baseDir = process.cwd()

    // 业务文件路径
    app.businessPath = path.resolve(app.baseDir, `.${sep}app`)

    // 初始化环境配置
    app.env = env();
    console.log("--[start] app.env:", app.env.get());

    //加载 middleware
    middlewareLoader(app)
    console.log("--[start] load middleware done ---");

    // 加载 routerSchema
    routerSchemaLoader(app)
    console.log("--[start]  load routerSchema done --");

    // 加载contorller
    controllerLoader(app)
    console.log("--[start] load controller done --");

    // 加载service
    serviceLoader(app)
    console.log("--[start]  load service done --");

    // 加载config
    configLoader(app)
    console.log("--[start]  load config done --");

    // 加载extend
    extendLoader(app)
    console.log("--[start] load extend done --");

    // 注册全局中间件
    try {
      require(`${app.businessPath}${sep}middleware.js`)(app);
      console.log("--[start] load global middleware done --");
    } catch (e) {
      console.error('[exception] there is no global middleware file.');
    }

    // 加载router
    routerLoader(app)
    console.log(" ~ start ~ load router done --");

    // 启动服务
    try {
      const port = process.env.PORTB || 8080;
      const host = process.env.IP || '0.0.0.0';
      app.listen(port, host);
      console.log(`\n --- 🚀 Server running at http://localhost:${port} 🚀 --- \n`);
    } catch (e) {
      console.error(e);
    }

    return app

  }
}