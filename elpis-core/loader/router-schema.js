const glob = require('glob');
const path = require('path')
const { sep } = path

/**
 * router-schema loader
 * @param {object} app koa实例 
 * 
 * 通过 'json-schema' & 'ajv' 对 api规则进行约束,配合 api-params-verify 中间件使用
 * 
 * app/router-schema/api1.js  // { 'api1/data/getDetail' :{} } 
 * app/router-schema/api2.js  // { 'api2/data/getDetail' :{} } 
 * ...
 * 
 * 输出:
 *   app.routerSchema = {
 *      'api1/data/getDetail':{},
 *      'api2/data/getDetail':{},
 *     ...
 *   }
 */

module.exports = (app) => {
  // 读取app/router-schema/**/**.js 所有文件 
  const middlewarePath = path.resolve(app.businessPath, `.${sep}router-schema`);
  const fileList = glob.sync(path.resolve(middlewarePath, `.${sep}**${sep}**.js`));

  // 注册所有 routerSchema, 使得 app.routerSchema 可以访问
  let routerSchema = {}
  fileList.forEach(file => {
    routerSchema = {
      ...routerSchema,
      ...require(path.resolve(file))
    }
  });
  app.routerSchema = routerSchema
}