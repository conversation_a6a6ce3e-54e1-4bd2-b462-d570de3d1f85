const glob = require('glob');
const path = require('path')
const { sep } = path

/**
 *  controller loader
 * @param {object} app koa 实例
 * 
 * 加载所有 controller,,可通过`app.controller.${目录}.${文件}`访问 
 * 
 * 例子:
 * app/controller
 *   |
 *   | -- custom-module
 *           |
 *           | -- custom-controller.js
 * 
 * => app.controller.customname.customController
 */

module.exports = (app) => {
  // 读取app/controller/**/**.js 所有文件
  const controllerPath = path.resolve(app.businessPath, `.${sep}controller`);
  const fileList = glob.sync(path.resolve(controllerPath, `.${sep}**${sep}**.js`));

  // 遍历所有文件目录,把内容加载到 app.controller 下
  const controller = {}
  fileList?.forEach(file => {
    //提取文件名
    let name = path.resolve(file)

    // 截取路径 app/controller/custom-module/custom-controller.js => custom-module/custom-controller
    name = name.substring(name.lastIndexOf(`controller${sep}`) + `controller${sep}`.length, name.lastIndexOf('.'));

    // 把'-'统一改为驼峰式, custom-module/custom-controller.js => customModule/customController
    name = name.replace(/[_-][a-z]/ig, (s) => s.substring(1).toUpperCase());

    //挂载 controller 到内存 app 对象中;  tempController === { customModule:{ customController:{ } } }
    let tempController = controller;
    const names = name.split(sep)
    for (let i = 0, len = names.length; i < len; ++i) {
      if (i === len - 1) {
        const ControllerModule = require(path.resolve(file))(app)
        tempController[names[i]] = new ControllerModule()
        return
      } else {
        if (!tempController[names[i]]) {
          tempController[names[i]] = {}
        }
        tempController = tempController[names[i]]
      }
    }
  })
  app.controller = controller
}